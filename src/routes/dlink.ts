import { Hono } from "hono";

export const dlink = new Hono();

dlink.get("*", async (c) => {
  const url = new URL(c.req.url);
  const path = url.href.replace(url.origin + "/d/", "");

  const token = "*********************************************************************************************";
  const url2 = "https://raw.githubusercontent.com/rel233/notes/main/" + path;
  const res = await fetch(url2, { headers: { Authorization: "token " + token }, cf: { cacheTtl: 0 } });

  return new Response(res.body, res);
});
