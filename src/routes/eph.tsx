import { Hono } from "hono";

export const eph = new Hono();

const CLIENT_ID = "cli_q80009293232ab6";
const CLIENT_SECRET = "b3b1d85a6c9b1c2a1d2921ef8fb00d0ecabc4bd8cc6dc5080fab961176a6fe71";
const BASE_URL = "https://app.alice.ws/cli";

const cusapi = (url, options) => {
  return fetch(url, options);
};

eph.get("/list", async (c) => {
  const res = await fetch("https://app.alice.ws/cli/v1/Evo/Plan", {
    headers: { Authorization: `Bearer ${CLIENT_ID}:${CLIENT_SECRET}` },
  });
  const data = await res.json();

  console.log("list", data);
  return c.text(JSON.stringify(data, null, 2));
});
