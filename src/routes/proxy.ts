import { Hono } from "hono";

export const proxy = new Hono();

proxy.all("*", async (c) => {
  const url = new URL(c.req.url);
  const to = url.href.replace(url.origin, "").replace(/^\/[^\/]+\//, "");

  const req = new Request(to, {
    headers: c.req.raw.headers,
    method: c.req.raw.method,
    body: c.req.raw.body,
    redirect: "follow",
  });

  const res = await fetch(req);
  const res2 = new Response(res.body, res);
  res2.headers.set("Access-Control-Allow-Origin", "*");

  return res2;
});
