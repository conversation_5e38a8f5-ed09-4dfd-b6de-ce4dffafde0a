import { Hono } from "hono";

export const tts = new Hono();

const genRanHex = (size = 16) => [...Array(size)].map(() => Math.floor(Math.random() * 16).toString(16)).join("");

const getTTS = (t: string, r = "0", v = "zh-CN-XiaoxiaoNeural", o = "webm-24khz-16bit-mono-opus"): Promise<ArrayBuffer> => {
  return new Promise(async (resolve) => {
    const arr: ArrayBuffer[] = [];

    const res = await fetch("https://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=6A5AA1D4EAFF4E9FB37E23D68491D6F4", { headers: { Upgrade: "websocket" } });
    const ws = res.webSocket;
    if (!ws) throw new Error("not websocket");

    ws.accept();
    ws.addEventListener("message", (msg) => {
      if (typeof msg.data === "string") {
        const str = msg.data.toString();
        if (str.includes("Path:turn.end")) {
          const len = arr.reduce((p, c) => p + c.byteLength, 0);
          const arr2 = new Uint8Array(len);

          let offset = 0;
          for (const buf of arr) {
            arr2.set(new Uint8Array(buf), offset);
            offset += buf.byteLength;
          }

          resolve(arr2.buffer);
        }
      } else {
        const str = new TextDecoder().decode(msg.data);
        const spt = "Path:audio\r\n";
        const idx = str.indexOf(spt) + spt.length;
        const data = msg.data.slice(idx);
        arr.push(data);
      }
    });

    const config =
      `X-Timestamp:${Date()}\r\nContent-Type:application/json; charset=utf-8\r\nPath:speech.config\r\n\r\n` +
      `{"context":{"synthesis":{"audio":{"metadataoptions":{"sentenceBoundaryEnabled":"false","wordBoundaryEnabled":"false"},"outputFormat":"${o}"}}}}`;

    ws.send(config);

    const rid = genRanHex(32);
    console.log(rid);
    const ssml =
      `X-Timestamp:${Date()}\r\nX-RequestId:${rid}\r\nContent-Type:application/ssml+xml\r\nPath:ssml\r\n\r\n` +
      `<speak xmlns="http://www.w3.org/2001/10/synthesis" xmlns:mstts="http://www.w3.org/2001/mstts" xmlns:emo="http://www.w3.org/2009/10/emotionml" version="1.0" xml:lang="en-US">
        <voice name="${v}">
          <prosody rate="${r}%" pitch="0%">${t}</prosody>
        </voice>
      </speak>`;

    ws.send(ssml);
  });
};

tts.get("/", async (c) => {
  const { t, r, v, o } = c.req.query();

  const buf = await getTTS(t, r, v, o);
  return c.body(buf, { headers: { "Content-Type": "audio/webm" } });
});
