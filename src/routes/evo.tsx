import { Hono } from "hono";

export const evo = new Hono();

const token = `66723b498cce5009dd1071d7b13146852d9a4869576b3e361e91dbf732d507aa`;
const baseApiUrl = `https://app.alice.ws/cli`;

const api = async (url: string, config?: RequestInit) => {
  const res: any = await fetch(baseApiUrl + url, {
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      "KP-APIToken": token,
    },
    ...config,
  }).then((res) => res.json());
  return res;
};

evo.get("/add", async (c) => {
  const time = c.req.query("time") || 24;
  const pid = c.req.query("pid") || 1;
  const plans = [38, 39, 40, 41];

  let ins = await api("/v1/Evo/Instance").then((res) => res?.data?.[0]);

  if (!ins) {
    const sid = await api("/v1/User/SSHKey").then((res) => res?.data?.[0]?.id);
    ins = await api("/v1/Evo/Deploy", {
      method: "post",
      body: JSON.stringify({
        product_id: plans[+pid],
        // os_id: 1,
        os_id: 9,
        time: time,
        sshKey: sid,
      }),
    });
  }

  return c.json(ins);
});

evo.get("/del", async (c) => {
  const ins = await api("/v1/Evo/Instance").then((res) => res?.data?.[0]);
  const res = await api("/v1/Evo/Destroy", { method: "post", body: JSON.stringify({ id: ins.id }) });
  return c.json(res);
});
