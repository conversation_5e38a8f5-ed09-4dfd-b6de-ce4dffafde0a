import { Hono } from "hono";

export const push = new Hono();

push.get("/tg", async (c) => {
  const token = "8059247744:AAETGPkwLJta85gAhNGr4phbSrfbGu9cAvE";
  const base = `https://api.telegram.org/bot${token}`;
  const chat_id = "1377579978";

  const text = c.req.query("text");
  const res = await fetch(base + "/sendMessage", {
    method: "post",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      chat_id,
      text,
    }),
  });

  return res;
});

push.get("/wx", async (c) => {
  const url = `https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a5882b24-9f51-4811-9071-785e5b51e3c8`;
  const text = c.req.query("text");
  return fetch(url, {
    method: "post",
    body: JSON.stringify({
      msgtype: "text",
      text: { content: text },
    }),
  });
});

push.post("/wx", async (c) => {
  const url = `https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a5882b24-9f51-4811-9071-785e5b51e3c8`;
  const body = await c.req.json();
  const text = body.text;
  return fetch(url, {
    method: "post",
    body: JSON.stringify({
      msgtype: "text",
      text: { content: text },
    }),
  });
});

const nid = "4b3cd7fd-e2b5-4b3e-b570-c9c4dcb18199";

push.get("/ntfy/id", async (c) => {
  return c.text(nid);
});

push.get("/ntfy", async (c) => {
  const url = `https://ntfy.sh/` + nid;
  const text = c.req.query("text") || "";
  const title = c.req.query("title") || "";

  return fetch(url, {
    method: "post",
    headers: { Title: title },
    body: text,
  });
});
